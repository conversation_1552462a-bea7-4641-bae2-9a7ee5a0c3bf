"use client";

import React, { useState } from "react";
import Link from "next/link";
import { ICommunity } from "@/models/Community";
import { Users, DollarSign } from "lucide-react";

interface CommunityfeedProps {
  communitys: ICommunity[];
}

// Separate component for banner image with proper state management
interface BannerImageProps {
  community: ICommunity;
}

const BannerImage: React.FC<BannerImageProps> = ({ community }) => {
  const [showFallback, setShowFallback] = useState<boolean>(false);

  // Enhanced fallback gradient with community name initial and pattern
  const firstLetter = community.name?.charAt(0).toUpperCase() || "C";
  const gradients = [
    "from-orange-100 via-orange-50 to-orange-100",
    "from-blue-100 via-blue-50 to-blue-100",
    "from-green-100 via-green-50 to-green-100",
    "from-purple-100 via-purple-50 to-purple-100",
    "from-pink-100 via-pink-50 to-pink-100",
  ];

  const gradientIndex = community.name
    ? community.name.length % gradients.length
    : 0;
  const selectedGradient = gradients[gradientIndex];

  // Fallback banner component
  const FallbackBanner = () => (
    <div
      className={`w-full h-full rounded-t-xl bg-gradient-to-br ${selectedGradient} relative overflow-hidden`}
    >
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-4 left-4 w-16 h-16 border-2 border-orange-300 rounded-full"></div>
        <div className="absolute top-8 right-8 w-8 h-8 border border-orange-300 rounded-full"></div>
        <div className="absolute bottom-6 left-8 w-12 h-12 border border-orange-300 rounded-full"></div>
        <div className="absolute bottom-4 right-4 w-6 h-6 border border-orange-300 rounded-full"></div>
      </div>

      {/* Main content */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <div className="w-16 h-16 bg-orange-200/50 backdrop-blur-sm rounded-2xl flex items-center justify-center border border-orange-300/50">
            <span className="text-2xl font-bold text-orange-600">
              {firstLetter}
            </span>
          </div>
          <div className="text-center">
            <div className="text-orange-700/80 text-sm font-medium truncate max-w-32">
              {community.name}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // If no banner image URL or we should show fallback, return fallback
  if (
    !community?.bannerImageurl ||
    community.bannerImageurl.trim() === "" ||
    showFallback
  ) {
    return <FallbackBanner />;
  }

  // Try to show the banner image with fallback on error
  return (
    <div className="w-full h-full relative">
      <img
        src={community.bannerImageurl}
        alt={`${community.name} banner`}
        className="w-full h-full rounded-t-xl object-cover"
        onError={() => {
          console.log(
            `Failed to load banner for ${community.name}: ${community.bannerImageurl}`
          );
          setShowFallback(true);
        }}
        onLoad={() => {
          console.log(`Successfully loaded banner for ${community.name}`);
        }}
      />
      {/* Fallback shown behind the image in case of loading issues */}
      <div className="absolute inset-0 -z-10">
        <FallbackBanner />
      </div>
    </div>
  );
};

function truncateDescription(description: string | undefined): string {
  if (!description) {
    return "";
  }
  const words = description.split(" ");
  if (words.length > 30) {
    return words.slice(0, 30).join(" ") + "...";
  }
  return description;
}

export default function Communityfeed({ communitys }: CommunityfeedProps) {
  // Early return if communitys is undefined or null
  if (!communitys || !Array.isArray(communitys)) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">No communities available.</p>
      </div>
    );
  }

  // Function to render the appropriate icon image component
  const renderIconImage = (community: ICommunity) => {
    if (community.iconImageUrl) {
      return (
        <img
          src={community.iconImageUrl}
          alt={`${community.name} icon`}
          className="w-10 h-10 rounded-md object-cover border border-gray-200 shadow-sm"
        />
      );
    }

    // Enhanced fallback icon with community initial and gradient
    const firstLetter = community.name?.charAt(0).toUpperCase() || "C";
    const iconGradients = [
      "from-primary/90 to-primary",
      "from-primary/90 to-primary",
      "from-primary/90 to-primary",
      "from-primary/90 to-primary",
      "from-primary/90 to-primary",
    ];

    const gradientIndex = community.name
      ? community.name.length % iconGradients.length
      : 0;
    const selectedGradient = iconGradients[gradientIndex];

    return (
      <div
        className={`w-10 h-10 rounded-md bg-gradient-to-br ${selectedGradient} flex items-center justify-center border border-primary/20 shadow-sm`}
      >
        <span className="text-primary-foreground text-sm font-bold">
          {firstLetter}
        </span>
      </div>
    );
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
      {communitys.map((community) => (
        <Link
          key={community._id?.toString()}
          href={community.slug ? `/Newcompage/${community.slug}/about` : "#"}
          className="rounded-lg shadow-sm border min-h-[450px] sm:h-[400px] overflow-hidden hover:shadow-md transition-all duration-200 block community-card w-full"
          style={{
            backgroundColor: "var(--card-bg)",
            color: "var(--text-primary)",
            borderColor: "var(--card-border)",
          }}
        >
          {/* Banner */}
          <div className="w-full h-48 sm:h-40 relative overflow-hidden">
            <BannerImage community={community} />
          </div>

          {/* Community Icon and Name */}
          <div className="p-4 sm:p-5 flex flex-col h-[calc(450px-192px)] sm:h-[calc(400px-160px)]">
            <div className="flex items-center gap-3 mb-4 sm:mb-3">
              {renderIconImage(community)}
              <h2 className="font-bold text-lg sm:text-lg truncate">
                {community.name}
              </h2>
            </div>

            <p
              className="text-sm sm:text-sm mb-4 flex-grow line-clamp-4 leading-relaxed"
              style={{ color: "var(--text-secondary)" }}
            >
              {truncateDescription(community.description)}
            </p>

            <div
              className="mt-auto pt-4 border-t"
              style={{ borderColor: "var(--border-color)" }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  <Users
                    className="h-4 w-4"
                    style={{ color: "var(--text-secondary)" }}
                  />
                  <span className="text-sm font-medium">
                    {(community.members?.length || 0).toLocaleString()} Members
                  </span>
                </div>

                <div className="flex items-center gap-1">
                  {community.price && community.price > 0 ? (
                    <>
                      <DollarSign
                        className="h-4 w-4"
                        style={{ color: "var(--text-secondary)" }}
                      />
                      <span className="text-sm font-medium">
                        {community.currency === "INR" ? "₹" : "$"}
                        {community.price}
                        {community.pricingType === "monthly"
                          ? "/month"
                          : community.pricingType === "yearly"
                            ? "/year"
                            : ""}
                      </span>
                    </>
                  ) : (
                    <span className="text-sm font-medium text-green-600">
                      Free
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        </Link>
      ))}
    </div>
  );
}
