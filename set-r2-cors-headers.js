// <PERSON><PERSON><PERSON> to set Cross-Origin-Resource-Policy headers on R2 objects
import { S3Client, ListObjectsV2Command, CopyObjectCommand, HeadObjectCommand } from "@aws-sdk/client-s3";
import dotenv from "dotenv";
import https from "https";
import crypto from "crypto";

// Initialize dotenv
dotenv.config({ path: ".env.development" });

// Log environment variables (without sensitive data)
console.log("Environment variables:");
console.log("R2_ENDPOINT_URL:", process.env.R2_ENDPOINT_URL);
console.log("R2_BUCKET_NAME:", process.env.R2_BUCKET_NAME);
console.log("R2_ACCESS_KEY_ID exists:", !!process.env.R2_ACCESS_KEY_ID);
console.log("R2_SECRET_ACCESS_KEY exists:", !!process.env.R2_SECRET_ACCESS_KEY);

// Create a custom HTTPS agent with modern TLS settings
const httpsAgent = new https.Agent({
  rejectUnauthorized: true,
  secureProtocol: "TLS_method",
  secureOptions: crypto.constants?.SSL_OP_NO_SSLv3 || 0,
  ciphers:
    "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384",
});

// Configure S3 client for R2 with custom HTTPS agent
console.log("Configuring S3 client for R2 with custom HTTPS agent...");
const s3Client = new S3Client({
  region: "auto",
  endpoint: process.env.R2_ENDPOINT_URL,
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID,
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY,
  },
  forcePathStyle: true, // Required for Cloudflare R2
  requestHandler: {
    httpOptions: {
      agent: httpsAgent,
    },
  },
});

const bucketName = process.env.R2_BUCKET_NAME;

// Function to check if an object is an image
function isImageFile(key) {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp', '.ico'];
  return imageExtensions.some(ext => key.toLowerCase().endsWith(ext));
}

// Function to get content type from file extension
function getContentType(key) {
  const ext = key.toLowerCase().split('.').pop();
  const contentTypes = {
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'webp': 'image/webp',
    'svg': 'image/svg+xml',
    'bmp': 'image/bmp',
    'ico': 'image/x-icon'
  };
  return contentTypes[ext] || 'application/octet-stream';
}

// Function to update object metadata with CORS headers
async function updateObjectCorsHeaders(key) {
  try {
    console.log(`Processing: ${key}`);
    
    // Get current object metadata
    const headCommand = new HeadObjectCommand({
      Bucket: bucketName,
      Key: key,
    });
    
    const headResponse = await s3Client.send(headCommand);
    console.log(`Current metadata for ${key}:`, headResponse.Metadata);
    
    // Copy object to itself with new metadata
    const copyCommand = new CopyObjectCommand({
      Bucket: bucketName,
      Key: key,
      CopySource: `${bucketName}/${key}`,
      ContentType: getContentType(key),
      CacheControl: 'public, max-age=31536000', // 1 year cache
      Metadata: {
        ...headResponse.Metadata,
        'cors-policy': 'cross-origin',
      },
      MetadataDirective: 'REPLACE',
      ACL: 'public-read',
      // Add CORS headers
      ResponseCacheControl: 'public, max-age=31536000',
      ResponseContentType: getContentType(key),
    });
    
    await s3Client.send(copyCommand);
    console.log(`✅ Updated CORS headers for: ${key}`);
    
  } catch (error) {
    console.error(`❌ Error updating ${key}:`, error.message);
  }
}

// Main function to process all image objects
async function setCorsHeadersOnImages() {
  try {
    console.log("Starting to set CORS headers on image objects...");
    
    let continuationToken;
    let totalProcessed = 0;
    
    do {
      const listCommand = new ListObjectsV2Command({
        Bucket: bucketName,
        ContinuationToken: continuationToken,
        MaxKeys: 100, // Process in batches
      });
      
      const listResponse = await s3Client.send(listCommand);
      
      if (listResponse.Contents) {
        // Filter for image files only
        const imageObjects = listResponse.Contents.filter(obj => 
          obj.Key && isImageFile(obj.Key)
        );
        
        console.log(`Found ${imageObjects.length} image objects in this batch`);
        
        // Process each image object
        for (const obj of imageObjects) {
          await updateObjectCorsHeaders(obj.Key);
          totalProcessed++;
          
          // Add a small delay to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }
      
      continuationToken = listResponse.NextContinuationToken;
      
    } while (continuationToken);
    
    console.log(`\n🎉 Completed! Processed ${totalProcessed} image objects.`);
    
  } catch (error) {
    console.error("Error setting CORS headers:", error);
    console.error("Error details:", error.message);
    if (error.stack) {
      console.error("Stack trace:", error.stack);
    }
  }
}

// Run the script
setCorsHeadersOnImages();
